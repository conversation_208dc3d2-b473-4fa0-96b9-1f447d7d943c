/* CSS Document */


/* 
*
*
[Table of contents]
*
*
*
    font / @import url
    preloader / .preloader 
    general / body
    navigation / .p-3-vh
    main menu  / .white nav
    menu right / .menu-right
    breadcumb / .breadcumb
    content / .imgbg-col
    timeline owl / .timeline-container
    team / .team
    diagram / .diagram-logo
    col-blog / .col-blog
    accordion / .accordion 
    map / .mapouter
    contact / .text-side
    footer / footer
    totop / #totop
    Blog / .blog-simple
    post meta / .post-meta 
    single post / .top-article 
    aside, sidebar / aside 
    Blog comment / #blog-comment
    comment form / .comment-respond 
    search menu / .search-container
    scroll onStep / .onStep

*
*
*/

/* font */
@import url('https://fonts.googleapis.com/css2?family=Red+Hat+Display:wght@400;500;700&display=swap');

/* general */
body {
font-family: 'Red Hat Display', sans-serif;
font-style: normal;
font-weight: 400;
background: #020b1c;
color: #6f6f6f;
font-size: 16px;
line-height: 1.6;
letter-spacing: .3px;
}
section {
position: relative;
background: #fff;
padding: 120px 0;
}
.no-padding, section.no-padding{
padding: 0 !important;
}
.no-top{
padding-top: 0px !important;
}
.no-bottom{
padding-bottom: 0px !important;
}
.centered {
display: flex;
align-items: center;
justify-content: center;
}
.centeredleft {
display: flex;
align-items: center;
justify-content: left;
}
.content-wrapper {
position: relative;
overflow: hidden;
}
.bgpurp{
background: #543884;
color: #fff;
}
.bgimg{
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
}
.bgimg:before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #543884; 
    opacity: .8;
}
.bgimg-section{
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    background-attachment: fixed !important;
    background-position: center !important;
}
.bgimg-section:before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #543884; 
    opacity: .86;
}
.bgpurp-dark{
background: #452a74;
color: #fff;
}
.bgpurp .fly{
  position: relative;
  margin: 0 5% -100px;
  max-width: 1600px;
  top: -100px;
  padding: 45px 0;
  border-radius: 6px;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
  background-position: center !important;
  z-index: 1;
}
.bgpurp .fly:before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #452a74; 
    opacity: .9;
    border-radius: 6px;
}
section.s-img, .s-img {
    position: relative;
    height: auto;
    color: #fff;
    background-position: center;
    background-repeat: repeat;
}
.s-img.colf {
    position: absolute;
    width: 100%;
    height: 100%;
    color: #fff;
    background-position: top;
    background-repeat: no-repeat;
    background-size: 100%;
}
.s-img .detailcontent .heading, .s-img .detailcontent .subheading {
    color: #fff !important;
}
.s-img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.6);
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6{
  font-weight: 600;
}
.color{
    color: #ec4176 !important;
}
.h1.color{
    font-size: 3.6rem;
}
.bgblack .h1, .bgblack .h2, .bgblack .h3, 
.bgblack .h4, .bgblack .h5, .bgblack .h6, 
.bgblack h1, .bgblack h2, .bgblack h3, 
.bgblack h4, .bgblack h5, .bgblack h6, .dark h1, .dark h2, .dark h3, 
.dark h4, .dark h5, .dark h6, .bgblack .detailcontent .heading, .bgblack a.card-title{
color:#fff; 
}
.dark{
background: #020917 ;
color: #fff;
}
/*** preloader ***/
#preloader {
  display: block;
  position: fixed;
  width: 100%;
  height: 100vh;
  background: #020b1c;
  z-index: 99999;
}
.animsition-overlay-slide{
    background: #020b1c;
    z-index: 99999;
}
#preloader .loader {
  font-size: 1.6rem;
  top: 43%;
  margin: 0;
  margin-bottom: 50px;
  position: relative;
  display: block;
  opacity: 1;
  text-align: center;
  color: #fff;
  z-index: 9999;
}
#preloader .loader span{
    display: block;
    margin-bottom: 15px;
}
.progressbar{
    margin-top: 1rem;
    height: 0.1rem;
    width: 10rem;
    background: #b3b3b3;
    margin: auto;
}
.progressbar::after{
    content: "";
    width: 3rem;
    height: 0.11rem;
    background: #ec4176;
    display: block;
    border-radius: 0.5rem;
    animation: progessbar 1.5s cubic-bezier(0.65, 0.05, 0.36, 1) infinite;
}
@keyframes progessbar{
    0% {
        transform: translateX(0rem);
    }
    50% {
        transform: translateX(7rem);
    }
    100% {
        transform: translateX(0rem);
    }
  }

/*** navigation ***/
.p-3-vh {
display: flex;
justify-content: space-between;
padding-left: 5%;
padding-right: 5%;
max-width: 1600px;
margin: 0 auto;
}
header{
top: 0px;
left: 0px;
position: fixed;
z-index: 999;
}
header.init {
margin-bottom: 0px;
width: 100%;
padding: 15px 0;
background: rgba(6,17,33,0);
box-shadow: 0px 10px 60px rgba(0, 0, 0, 0);
-webkit-transition: all .5s cubic-bezier(.165, .84, .44, 1);
-moz-transition: all .5s cubic-bezier(.165, .84, .44, 1);
transition: all .5s cubic-bezier(.165, .84, .44, 1);
}
header.init.sub{
    top: 30px;
}
header.show {
top: 0px;
left: 0px;
padding: 0 0;
box-shadow: 0px 10px 60px rgba(0, 0, 0, .1);
background: rgba(6,17,33,1) !important;
animation-name: animatedheader;
animation-duration: .3s;
-webkit-transition: all .5s cubic-bezier(.165, .84, .44, 1);
-moz-transition: all .5s cubic-bezier(.165, .84, .44, 1);
transition: all .5s cubic-bezier(.165, .84, .44, 1);
}
header.init.sub.show{
    top: 0px;
}
.slideUp {
  -webkit-transform: translateY(-100px);
  transform: translateY(-100px);
  transition: transform .6s ease-out;
}
.slideDown {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  transition: transform .6s ease-out;
}

/*logo*/
.logo {
position: relative;
top: 0px;
left: 0px;
margin: 10px 0;
margin: 0px;
height: auto;
z-index: 2;
}
.logo img{
max-height: 30px;
}
.logo img.show{
display: none;
}
header.show img.show{
display: block;
}
header.show img.init{
display: none;
}

/* main menu */
.white nav {
position: relative;
float: right;
top: 2px;
z-index: 999;
}
.white nav ul {
padding: 0;
margin: 0;
list-style: none;
position: relative;
}
.white nav ul li {
display: inline-block;
margin: 0;
margin-right: 20px;
}
.white nav ul li:last-child{
margin-right: 0px;
border-bottom: 0px;
}
.white nav ul li .input-group {
top: 15px;
margin-left: 20px;
width: 220px;
}
.white nav ul li .input-group button {
display: inherit;
padding: 0;
line-height: 0px;
border: none;
}
.white nav ul li .input-group i {
margin-left: 0px;
}
.white nav a {
color: #fff;
display: block;
padding: 28px 20px;
font-size: .93rem;
font-weight: 700;
letter-spacing: .3px;
text-decoration: none;
text-transform: uppercase;
-webkit-transition: all .1s ease-in-out;
-moz-transition: all .1s ease-in-out;
transition: all .1s ease-in-out;
}
.white nav a:hover {
color: #ddd !important;
-webkit-transition: all .1s ease-in-out;
-moz-transition: all .1s ease-in-out;
transition: all .1s ease-in-out;
}
.white nav a:hover::before {
display: inline-block;
position: absolute;
max-width: 35px;
color: rgba(255, 255, 255, 0);
border-bottom: 2px solid rgba(255, 255, 255, 1);
-webkit-transition: max-width 0.5s;
-moz-transition: max-width 0.5s;
transition: max-width 0.5s;
}
.white nav a::before {
position: absolute;
overflow: hidden;
max-width: 0;
border-bottom: 2px solid rgba(255, 255, 255, 0);
color: rgba(255, 255, 255, 0);
content: attr(data-hover);
-webkit-transition: max-width 0.5s;
-moz-transition: max-width 0.5s;
transition: max-width 0.5s;
white-space: nowrap;
}
.white nav a.actived {
color: #ddd !important;
}
.white nav ul ul {
display: none;
position: absolute;
top: 78px;
color: #efefef;
}
.white nav ul li:hover>ul {
display: inherit;
-webkit-animation-name: animfadeInUpmenu;
animation-name: animfadeInUpmenu;
-webkit-animation-duration: .3s;
animation-duration: .3s;
-webkit-animation-fill-mode: both;
animation-fill-mode: both;
}
.white nav ul ul li {
width: 200px;
float: none;
display: list-item;
background: #061121;
border-bottom: 1px groove rgba(0, 0, 0, .1);
box-shadow: 5px 5px 2px rgba(23, 36, 52, .05);
position: relative;
}
.white nav ul li i {
margin-left: 10px;
}
.white nav ul ul li a {
font-weight: 600;
color: #fff;
font-size: 13px;
line-height: 5px;
padding: 20px 30px;
}
.white nav ul ul li a:hover {
color: #efefef !important;
background: #ec4176;
}
.white nav ul ul li a.active {
background: #f8f8f8;
}
.white nav ul ul li:last-child {
border-bottom: none;
}
.white nav ul ul ul li {
position: relative;
top: -75px;
left: 230px;
}
.white>nav>ul>li.has-children>a:after {
content: '\f107';
font-family: 'FontAwesome';
margin-left: 10px;
}
li>a:only-child:after {
content: '';
}
span.span-drop {
position: relative;
right: 0;
top: 0;
float: right;
cursor: pointer;
}
span.span-drop:after {
content: '\f107';
font-family: 'FontAwesome';
color: #fff;
width: 20px;
height: 20px;
margin-left: 2px;
text-align: center;
line-height: 20px;
display: inline-block;
}
a.actived span.span-drop:after{
color: #ddd !important;
}
.nav-icon{
display: none;
}
#signin {
color: #fff;
background: #ec4176;
display: block;
padding: 8px 25px;
font-size: .93rem;
font-weight: 700;
letter-spacing: .3px;
text-decoration: none;
text-transform: uppercase;
border-radius: 30px;
-webkit-transition: all .1s ease-in-out;
-moz-transition: all .1s ease-in-out;
transition: all .1s ease-in-out;
}
#signin:hover {
color: #fff !important;
background: #ff4981;
-webkit-transition: all .1s ease-in-out;
-moz-transition: all .1s ease-in-out;
transition: all .1s ease-in-out;
}

/* menu right */
.menu-right {
display: none;
padding: 36px 0;
}
.menu-right{
	width: max-content;
	padding: 0 15px;
}
.menu-right .iconright{
	position: relative;
	padding: 0;
	margin: 0;
	list-style: none;
	top: 3px;
}
.menu-right .iconright li{
	display: inline-block;
}
.menu-right .iconright li a{
	font-size: 1.1rem;
	color: #fff;
	text-decoration: none;
    padding: 35px 5px;
}
#showside{
    color: #fff;
    cursor: pointer;
}
/*** navigation end ***/


/* breadcumb */
.breadcumb{
  display: block;
  background: #313840;
  padding: 140px 0;
  background-size: cover;
  background-position: center;
}
.breadcumb.errorpage{
    background: url(../img/404.jpg);
    background-repeat: no-repeat !important;
    background-size: 100% auto !important;
    background-attachment: fixed !important;
}
.errorpage h1{
    font-size: 3.5rem;
    font-weight: bold;
}
.breadcumb .main{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
}
.main .bread{
  color: #fff;
  text-align: center;
  padding: 60px 0 0;
}
.bread .spacebread{
  padding: 0 5px;
  opacity: .7;
}
.bread .spacebread::before{
  display: inline-block;
  content:"";
  width: 4px;
  height: 2px;
  background: #fff;
  position: relative;
  top: -4px;
  margin-left: 2px;
}
.bread .spacebread::after{
  display: inline-block;
  content:"";
  width: 20px;
  height: 2px;
  background: #fff;
  position: relative;
  left: 4px;
  top: -4px;
  margin-right: 5px;
}
.main .bread-title{
  text-transform: uppercase;
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 2px;
  margin: 0 auto;
  display: block;
}
.main .bread-subtitle{
  margin: 15px auto 0;
  display: inline-block;
}
.main .bread-subtitle a{
  color: #fff;
  font-weight: 400;
  text-decoration: none;
}

/*** content ***/
.imgbg-col{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.imgbg-col.no-radius{
  border-radius: 0;
}
.imgbg-png{
  position: relative;
  width: 100%;
  height: auto;
  object-fit: cover;
  padding: 70px;
  z-index: 1;
}
.imgbg-fly{
  position: relative;
  width: auto;
  height: 100%;
}
.imgbg-fly-100{
  position: relative;
  width: 100%;
  height: 100%;
}
.detailcontent{
  position: relative;
  display: block;
  padding: 60px 5%;
  letter-spacing: .3px;
  z-index: 1;
}
.detailcontent.bgwhite{
    background: #fff;
    text-align: center;
    padding: 25px 20px;
}
.detailcontent .icon{
  width: max-content;
  height: 60px;
  display: flex;
  align-content: center;
  align-items: center;
}
.detailcontent .icon img{
  width: auto;
  height: 50px;
  object-fit: cover;
}
.detailcontent .quote{
    display: flex;
    margin: 30px 0;
    font-weight: 600;
    font-style: italic;
}
.detailcontent .quote::before{
    content: '"';
    position: relative;
    font-size: 12rem;
    padding-right: 10px;
    color: #f05500;
    top: -120px;
    height: 65px;
    left: -40px;
}
.detailcontent ul.listcontent{
    padding: 0;
    margin: 30px 0 0;
    display: block;
    list-style: none;
}
.detailcontent ul.listcontent li {
    display: flex;
    flex-direction: row;
    padding-bottom: 1rem;
}
.detailcontent ul.listcontent li .icon{
    height: max-content;
}
.detailcontent ul.listcontent li .icon::before {
  content: "✓";
  color: #fff;
  font-weight: bold;
  display: inline-block; 
  width: 1.3em;
  font-size: 1.3rem;
  margin-right: 5px;
}
.bgblack{
  background: #020917;
  color: #fff;
}
.bgcontent{
  background: #362159;
  color: #fff;
  border-radius: 4px;
  padding: 30px 5%;
  margin-top: 45px;
}
.detailcontent .subheading{
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1.2;
  color: #ddd;
}
.detailcontent .heading{
    position: relative;
    font-size: 2.7rem;
    font-weight: 600;
    margin: 1rem 0 1rem;
    line-height: 1.2;
}
.detailcontent .heading.line{
    padding-bottom: 15px;
}
.detailcontent .heading.line:before{
    background: #ec4176 none repeat scroll 0 0;
    content: "";
    width: 40px;
    height: 2px;
    bottom: 0px;
    right: 50%;
    margin-right: -25px;
    position: absolute;
}
.detailcontent .heading.line:after{
    background: #ec4176 none repeat scroll 0 0;
    content: "";
    width: 4px;
    height: 2px;
    bottom: 0px;
    right: 50%;
    margin-right: 18px;
    position: absolute;
}

.detailcontent .heading.line.left:before{
  left: 2%;
}
.detailcontent .heading.line.left:after{
  left: 0%;
}
.detailcontent .textdetail{
  font-weight: 400;
  margin-bottom: 1rem;
}
.btn-content { 
  display: block;
  position:relative;
  width: max-content;
  height: auto;
  font-size: 11pt;
  font-weight: 700;
  text-align: center;
  letter-spacing:1px;
  text-shadow: none;
  line-height: 1.2;
  padding: 14px 30px;
  border-radius: 6px;
  background: #ec4176;
  cursor: pointer;
  overflow: hidden;
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
  -moz-transition: all 0.6s ease;
  -ms-transition: all 0.6s ease;
  -o-transition: all 0.6s ease;
  transition: all 0.6s ease;
}
.btn-content a{
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 700;
    color: #fff;
    position: relative;
    top: -1px;
}
.btn-content:hover { 
    background: #262254;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}
.btn-content:hover .shine{
    -webkit-animation: sheen .9s alternate;
    animation: sheen .9s alternate;
} 
.btn-content:hover a{ 
    color:#fff;
}
#home{
    display: block;
    height: max-content;
}
.cryptobg{
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    background: rgba(106,26,202,.4);
    opacity: .6;
    z-index: 1;
}
.container-chart{
  margin: 0 auto !important;
}

/*** timeline owl ***/
.timeline-container .owl-dots .owl-dot span{
  position: relative;
  padding: 8px;
  border: 2px solid rgba(0,0,0,0);
  background: none;
  margin-top: 30px;
}
.timeline-container .owl-dots .owl-dot span:before{
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  width: 6px;
  height: 6px;
  border-radius: 20px;
  background: rgba(255,255,255,.8);
}
.timeline-container .owl-theme .owl-dots .owl-dot.active span, .timeline-container .owl-theme .owl-dots .owl-dot:hover span{
  border: 2px solid #ec4176 !important;
  background: none;
}
.timeline-container .owl-dots .owl-dot.active span:before{
  background: rgba(255,255,255,.8);
}
.timeline-container .owl-stage-outer {
    top: -20px;
}
.timeline-container {
    margin-top:30px;
    padding: 10px 80px 0;
}
.timeline-container {
    margin-top: 0px;
    border-top: 3px #ec4176 dotted;
}
.timeline-slide {
    padding: 15px;
    margin: 4.2rem 0 0 0;
    width: 100%;
    border-radius: 4px;
    background: #061121;
    position:relative;
}
.timeline-slide .circle {
    border: 3px rgba(255,255,255,.8) solid;
    background-color: #ec4176;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-redius: 50%;
    height: 16px;
    width: 16px;
    position: absolute;
    left: 50%;
    margin-left: -15px;
    top: -65px;
    z-index: 2;
    overflow: visible;
}
.timeline-slide .vertical-line {
    border-left: 3px #ec4176 dotted;
    height: 50px;
    position: absolute;
    left: 50%;
    margin-left: -8px;
    top: -50px;
    z-index: 1;
}
.timeline-copy h3{
  font-size: 1.4rem;
}
.timeline-copy p{
  font-size: .98rem;
  font-weight: 300;
  color: #efefef;
}

/* team */
.team{
  display: flex;
  align-items: center;
  margin: 30px 15px;
}
.team.v2{
  flex-direction: column;
  background: #43296f;
  padding: 20px 15px 15px;
}
.team .img{
  overflow: hidden;
  width: 35%;
  height: auto;
  border-radius: 100%;
  border: 9px solid #ec4176;
}
.team.v2 .img{
  width: 100%;
  height: 200px;
  border-radius: 4px;
  border: none;
}
.team .img img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.team .detail{
  width: 65%;
  padding-left: 20px;
}
.team.v2 .detail{
  width: 100%;
  padding: 15px 0 0;
}
.team .detail .name{
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
}
.team .detail .bio{
  margin-bottom: 15px;
}
.team .detail .icon-soc{
  display: inline-block;
}
.team .detail .icon-soc a{
  float: left;
  width: 40px;
  height: 40px;
  margin-right: 10px;
  text-align: center;
  line-height: 43px;
  color: #fff;
  background: #262254;
  text-decoration: none;
  border-radius: 4px;
  transition: all .3s ease-in-out;
}
.team .detail .icon-soc a:hover{
  background:#ec4176;
  transition: all .3s ease-in-out; 
}

/* diagram */
.diagram-logo{
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.diagram-logo .imglogo{
  padding: 40%;
  width: auto;
  height: auto;
}
.diagram-logo img{
  width: 100%;
  height: auto;
}
.canvas {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 800px;
}
.unit {
  fill: none;
  stroke-width: 10;
  animation: render 1s;
  transition: 1s;
  cursor: pointer;
}
.unit:hover {
  opacity: 1;
  stroke-width: 12;
}
.unit.red {
  stroke: #f85d77;
  stroke-dasharray: 40 100;
  stroke-dashoffset: -60;
}
.unit.blue {
  stroke: #5ad6f8;
  stroke-dasharray: 19 100;
  stroke-dashoffset: -1;
}
.unit.green {
  stroke: #61f89f;
  stroke-dasharray: 17 100;
  stroke-dashoffset: -21;
}
.unit.purple {
  stroke: #ac56f7;
  stroke-dasharray: 9 100;
  stroke-dashoffset: -39;
}
.unit.orange {
  stroke: #f8c04e;
  stroke-dasharray: 10 100;
  stroke-dashoffset: -49;
}
.textsvg {
  fill: #fff;
  font-size: 2.3px;
  font-weight: 700;
  letter-spacing: 0;
}
.list-diagram{
  list-style: none;
  padding: 0;
  margin: 0;
}
.list-diagram li {
  display: block;
  margin-bottom: 15px;
}
.list-diagram li .dot{
  position: relative;
  top: 8px;
  display: inline-block;
  width: 30px;
  height: 30px;
  margin-right: 15px;
  border-radius: 100%;
}

/* col-blog */
.col-blog{
  display: block;
  padding-top: 15px;
}
.col-blog .imgblog{
  width: 100%;
  height: 240px;
  overflow: hidden;
  border-radius: 4px;
}
.col-blog .imgblog img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.col-blog .col-detail{
  display: block;
  padding: 15px 0;
}
.col-blog .col-detail .heading{
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
}
.col-blog .col-detail .date{
  display: inline-block;
  font-size: .85rem;
  padding-top: 5px;
  font-weight: 600;
  color: #eee;
}
.col-blog .col-detail .date .list{
  text-decoration: underline;
  color: #eee;
  display: inline-block;
  padding: 5px 10px 5px 0;
}
.col-blog .col-detail .date .list .icon{
  margin-right: 5px;
  position: relative;
  top: -4px;
  height: 100%;
  display: inline-block;
}
.col-blog .col-detail .date .list .icon img{
  height: 100%;
  object-fit: cover;
}
.col-blog .col-detail .desc{
  padding: 15px 0;
}

/* accordion */
.accordion .card-header:after {
    font-family: 'FontAwesome';  
    content: "\f068";
    float: right; 
}
.accordion .card{
  border: 0px;
  background: none;
}
.accordion .card-header{
  font-size: 1.1rem;
  color: inherit;
  font-weight: 500;
  background: #ec4176;
  cursor: pointer;
  padding-left: 30px !important;
  padding-right: 30px !important;
  padding: 1.25rem 0;
  border-radius: 4px;
  text-decoration: none;
}
.card-header {
    border-bottom: 0px solid rgba(255,255,255,.125);
    margin-bottom: 10px;
}
.accordion .card-body{
  font-weight: 400;
  padding-left: 0;
  padding-right: 0;
}
.accordion .card-header.collapsed{
  background: #543884;
}
.accordion .card-header.collapsed:after {
    content: "\f067"; 
}

/* map */
.mapouter{
    width: 100%;
    height: 60vh;
}
#gmap_canvas{
    width: 100%;
    height: 60vh;
}
#map-1 {
width: 100%;
height: 60vh;
padding-top: 0px;
padding-bottom: 0px;
}
.map{
background: #fdfdfd;
}

/* contact */
.text-side{
padding:  0;
margin-top: 0;
}
.text-side .heading{
font-size: 1.6rem !important;
}
.text-side .address{
padding-top:15px;
line-height: 1.3;
}
.text-side .address .heading{
font-size: 1.3rem !important;
font-weight: 500;
letter-spacing: 1px;
margin-bottom: 15px;
}
.text-side .address .list{
position: relative;
max-width: 320px;
display: block;
padding: 15px 0 15px;
padding-left: 40px;
}
.text-side .address .list i{
position: absolute;
top: 15px;
left: 5px;
font-size: 1.3rem;
display: inline-block;
color: #fff;
}
.text-side .address .list a{
color: inherit;
text-decoration: none;
transition: all 0.3s ease;
}
.text-side .address .list a:hover{
color: #ffb41d;
transition: all 0.3s ease;
}
.heading-cont{
position: relative;
font-size: 16pt;
margin-top: 0px;
margin-bottom:15px;
padding-bottom:15px;
border-bottom: 1px solid rgba(255,255,255,.1);
}
.heading-cont:after{
content: "";
position: absolute;
bottom: -2px;
left: 0px;
width: 25%;
height: 3px;
background: #dd7608;
}
.form-group {
margin-bottom: 20px;
}
.form-control {
width:100% !important;
height: 53px !important;
padding: 4px 20px 6px 70px;
font-size: 14px;
line-height: 24px;
color: #333;
background: #fff;
border: 1px solid rgba(0,0,0,.1); 
border-radius: 0;
box-shadow: none;
}
.form-control:focus {
background: #fff;
color: #333;
border: 1px solid rgba(0,0,0,.3); 
box-shadow: none;
}
.user-message textarea {
min-height: 195px;
padding-top: 12px;
}
.contact-form form button {
margin-top: 10px;
}
.user-name,
.user-email,
.user-phone,
.user-url,
.user-message{
position: relative;
}
.user-name::before, 
.user-email::before, 
.user-phone::before, 
.user-message::before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 100%;
    background: #262254;
}
.user-name::after,
.user-email::after,
.user-phone::after,
.user-url::after,
.user-message::after {
font-size: 1.3rem;
position: absolute;
left: 21px;
top: 10px;
font-family: "FontAwesome";
color: #fff;
}
.user-name::after {
content: "\f007";
}
.user-email::after {
content: "\f003";
}
.user-phone::after {
content: "\f095";
}
.user-url::after {
content: "\f0ac";
}
.user-message::after {
content: "\f086";
}
.btn-contact {
font-size: .9rem;
font-weight: 700;
font-style:normal;
text-align:center;
padding: 10px 32px;
color: #fff;
border:2px solid rgba(0,0,0,0);
background: #ec4176;
border-radius: 6px;
outline: none;
text-shadow:none;
margin-top: 15px;
margin-left:0px;
cursor:pointer;
-moz-transition: all 0.3s ease;
-ms-transition: all 0.3s ease;
-o-transition: all 0.3s ease;
transition: all 0.3s ease;
}
.btn-contact:hover{ 
color:#fff;
background:#262254;
}
.btn{
display: block;
}
.error {
width: 100%;
display: none;
padding: 2px 10px 2px 10px;
font-size: 11px;
margin: 10px auto;
}
.success {
width: 100%;
display: none;
padding: 5px 0px 2px 0px;
font-size: 11px;
margin: 10px auto;
}
/* contact end */

/* footer */
footer{
    font-weight: 500;
    color: #fff;
    background: #061121;
    padding: 120px 0 60px;
}
footer .logo{
    margin-bottom: 30px;
}
footer .logo img{
    max-height: 45px;
}
footer .heading{
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
    line-height: 3rem;
    margin-bottom: 30px;
}
footer .description{
    color: #fff;
    line-height: 2;
}
footer .s-social{
    width: max-content;
    margin: 30px 0 0;
}
footer .s-social a{
    color: #fff;
    border: 1px solid rgba(255,255,255,.3);
    text-decoration: none;
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-right: 15px;
    border-radius: 6px;
    transition: all .3s ease-in-out;
}
footer .s-social a:hover{
    background: #ec4176;
    transition: all .3s ease-in-out;
}
footer .s-address{
    color: #fff;
}
footer .s-address .detail{
  display: block;
  margin-bottom: 10px;
}
footer .s-address a{
    position: relative;
    color: #fff;
    transition: all .3s ease-in-out;
}
footer .s-address a:before{
  display: block;
  position: absolute;
  bottom: -5px;
  left: 0px;
  content: " ";
  width: 3px;
  height: 2px;
  margin-top: 5px;
  background: rgba(255,255,255,.6);
}
footer .s-address a:after{
  display: block;
  position: absolute;
  bottom: -5px;
  left: 6px;
  content: " ";
  width: 65px;
  height: 2px;
  background: rgba(255,255,255,.6);
}
footer .s-address a:hover{
    text-decoration: none;
    opacity: .6;
    transition: all .3s ease-in-out;
}
#subsribe{
    position: relative;
    width: 100%;
    display: block;
    margin-bottom: 15px;
}
#subsribe input[type="email"], input[type="submit"] {
    border-radius: 4px;
    padding-left: 15px;
    padding-right: 15px;
    height: 46px;
}
#subsribe input[type="email"]{
    width: 100%;
    font-weight: 400;
    font-size: .87rem;
    background-color: #313840;
    border: solid #313840;
    color: #efefef;
    outline: none;
}
#subsribe input[type="submit"] {
    font-size: .9rem;
    position: absolute;
    width: max-content;
    top: 0px;
    right: 0px;
    font-weight: 700;
    background-color: #ec4176;
    border: solid #ec4176;
    color: white;
    cursor: pointer;
    outline: none;
}
.subfooter{
    display: flex;
    flex-direction: column;
    padding: 30px 0 0; 
    margin-top: 45px;
}
.subfooter::before{
    display: block;
    content: "";
    width: 100%;
    height: 1px;
    position: relative;
    background: rgba(255,255,255,.3);
}
.subfooter .copyright{
    font-size: .9rem;
    color: #ccc;
}
.subfooter .terms{
    font-size: .9rem;
    font-weight: 400;
    color: #ccc;
    text-decoration: none;
    transition: all .3s ease-in-out;
}
.subfooter .terms:hover{
    opacity: .6;
    transition: all .3s ease-in-out;
}
.subfooter .subinside{
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 45px 0 0;
}

/* totop */
#totop {
width: 35px;
height: 35px;
font-size: 10pt;
color: #fff;
background: #ec4176;
line-height: 32px;
position: fixed;
right: 20px;
text-align: center;
cursor: pointer;
z-index: 9;
border-radius: 50%;
-moz-transition: all 0.3s ease;
transition: all 0.3s ease;
}
#totop.init {
bottom: -100px;
-moz-transition: all 0.3s ease;
transition: all 0.3s ease;
}
#totop.show {
bottom: 20px;
-moz-transition: all 0.3s ease;
transition: all 0.3s ease;
}
#totop:hover {
background: #262254;
-moz-transition: all 0.3s ease;
transition: all 0.3s ease;
}
/* totop end */


/* Blog */
.blog-simple{
display: block;
margin-bottom: 60px;
}
.blog-simple img, .blog-simple a img{
    display: block;
    width: 100%;
    height: auto;
    margin-bottom: 15px;
}
.blog-simple .blog-text {
display: block;
position: relative;
width: 100%;
padding: 15px 15px 15px 0px; 
margin-bottom: 30px;
letter-spacing:.3px;
}
.blog-simple blockquote{ 
font-size: 15px;
font-style: italic;
font-weight: 400;
line-height: 1.6em;
padding: 30px;
margin: 30px 0 30px 0;
border-left: 5px solid #ec4176;
}
.blog-simple .blog-text a{
  color: #fff;
  text-decoration: none;
}
.blog-simple .blog-text h2 a{
    color: inherit;
}
.blog-simple .blog-text .heading { margin-top:15px;}
.blog-simple .blog-text h3 a{ font-weight:500;}
.blog-simple .blog-text span {
display: inline-block;
margin-bottom: 20px;
}
.blog-simple .blog-text span.date-post {
font-size: 13px;
font-weight: 400;
margin-left: 20px;
}
.blog-simple .blog-text span.date-post i {
padding-right: 7px;

}
.blog-simple .blog-text span.user-post {
font-size: 13px;
font-weight:400;
}
.blog-simple .blog-text span.user-post i {
padding-right: 7px;

}
.blog-simple .blog-text span.comment {
margin-left:20px;
font-size: 13px;
}
.blog-simple .blog-text span.comment a{
  color: inherit;
  text-decoration: none;
}
.blog-simple .blog-text span.comment a i {
padding-right: 7px;

}
.blog-simple .blog-text h3 {
font-size: 20px;
margin-bottom: 20px;
font-weight: 300;
line-height: 1.5;
}
.btn-blog{
display: block;
position:relative;
width: max-content;
height: auto;
color: #fff !important;
text-decoration: none; 
font-size: 12pt;
font-weight: 800;
text-align: center;
letter-spacing:1px;
text-shadow: none;
line-height: 15px;
padding: 13px 36px;
margin-top: 30px;
background: #ec4176;
cursor: pointer;
overflow: hidden;
-ms-transform: scale(1, 1);
transform: scale(1, 1);
-moz-transition: all 0.6s ease;
-ms-transition: all 0.6s ease;
-o-transition: all 0.6s ease;
transition: all 0.6s ease;
}
.btn-blog:hover{
color: #292929;
text-decoration: none; 
transition: all .3s cubic-bezier(.215,.61,.355,1); 
}
.blog-simple ul.pagination{
position: relative !important;
color: #fff;
top: 0px;
left: 0px;
margin-bottom: 0px;
padding: 0;
}
.blog-simple ul.pagination li a{  
font-size: 9.5pt;
font-weight: 700;
color: #fff;
background: none;
padding: .72rem 1.1rem;
border: 1px solid rgba(255,255,255,.1);
-moz-transition: all .3s ease;
-ms-transition: all .3s ease;
-o-transition: all .3s ease;
transition: all .3s ease;
}
.blog-simple ul.pagination li a:hover, .page-item.active .page-link{ 
color: #fff; 
background: #ec4176; 
border: 1px solid rgba(255,255,255,.1);
-moz-transition: all .3s ease;
-ms-transition: all .3s ease;
-o-transition: all .3s ease;
transition: all .3s ease;
}

/* post meta */
.post-meta {
background: #fcfcfc;
border-top: none;
text-align: center;
}
.post-meta .format {
border-bottom: 1px solid #333333;
padding: 10px 0 10px;
}
.post-meta i {
margin: 0;
}
.post-meta .date {
border-bottom: 1px solid #e6e6e6;
padding: 10px 0 10px;
}
.post-meta .date span {
text-align: center;
color: #efefef;
font-size: 12px;
font-weight: 500;
}
.post-meta .comments {
padding: 10px 0 10px;
}
.post-meta .comments a {
color: #efefef;
font-size: 10px;
font-weight: 500;
}
.post-meta .comments a:hover {
text-decoration: none;
}

/* post format */
.post-image {
margin-bottom: 20px;
}
ul.meta-post {
float: left;
margin: 0;
padding: 0;
list-style: none;
}
ul.meta-post li {
float: left;
margin: 0 10px 0 0;
padding: 0;
list-style: none;
}
ul.meta-post li a {
float: left;
font-size: 11pt;
font-weight: 500;
padding-top:20px;
}

/* single post */
.top-article {
display:block;
margin-top: 30px;
}
.bottom-article {
overflow: hidden;
padding: 10px 0 10px 0;
margin-top: 10px;
margin-bottom: 30px;
}
.bottom-article a.btn {
display: inline-block;
width:auto;
height: 40px;
font-size:10pt;
font-weight:600;
letter-spacing:1pt;
font-style:normal;
text-align:center;
line-height:38px;
color:#fff;
-webkit-border-radius: 4px;
-moz-border-radius: 4px;
border-radius: 4px;
outline: none;
text-shadow:none;
margin: 5px auto;
margin-left:0px;
padding: 0 30px 0 30px;
cursor:pointer;
-moz-transition: all 0.3s ease;
-ms-transition: all 0.3s ease;
-o-transition: all 0.3s ease;
transition: all 0.3s ease; 
}
.bottom-article a.btn:hover{
color:#999;
background:#efefef;
}

/*  aside, sidebar */
aside {
position: relative;
margin-bottom: 40px;
}
.sidebar-nav {
float: left;
width: 100%;
}
.right-sidebar {
margin: 0 auto;
}
.left-sidebar {
padding: 0 30px 0 0;
}
.left-sidebar .widget h3, .left-sidebar .widget .widget-content {
padding-right: 20px;
}
aside .widget, .widget {
margin-bottom: 40px;
}
.widget .search-container{
padding: 0;
width: 100%;
margin: 0 auto;
}
.widget .form-control {
width:100% !important;
height:auto !important;
padding: 7px 10px 7px 10px !important;
font-size: 13px;
line-height: 24px;
color: #f8f8f8;
background: none;
border: 1px solid rgba(255,255,255,.1);
border-radius: 0;
box-shadow: none;
}
.widget .form-control:focus {
border: 1px solid rgba(255,255,255,.3);
box-shadow: none;
}
.widget .recent div {
width: 100%;
display: inline-block;
padding-bottom: 25px;
}
.widget .recent div img {
float: left;
margin-right: 20px;
width: 65px;
height: 65px;
object-fit: cover;
}
.widget .recent div h6 {
font-size: 18px;
margin-top: 10px;
margin-bottom: 5px
}
.widget .recent div h6 a{
font-weight: 700;
color: inherit;
text-decoration: none;
}
.widget .recent div p {
line-height: 20px;
}
.widget .tags {
margin: 0;
margin-left: 0;
padding-left: 0;
}
.widget .tags div {
margin: 5px 5px 15px 0;
display: inline-block;
}
.widget .tags div a {
font-size: 14px;
color: inherit;
border: 0px solid rgba(255,255,255,0);
padding: 4px 10px;
background: #ec4176;
border-radius: 4px;
text-align: center;
-moz-transition: all .3s ease;
-ms-transition: all .3s ease;
-o-transition: all .3s ease;
transition: all .3s ease;
}
.widget .tags div a:hover {
background: #452a74;
text-decoration: none;
-moz-transition: all .3s ease;
-ms-transition: all .3s ease;
-o-transition: all .3s ease;
transition: all .3s ease;
}
.devider-widget {
display: block;
margin-bottom: 30px;
margin-top: 20px;
text-align: left;
}
.devider-widget::before {
    display: inline-block;
    content: "";
    width: 4px;
    height: 2px;
    background: #ec4176;
    position: relative;
    top: -4px;
    margin-left: 2px;
}
.devider-widget::after {
    display: inline-block;
    content: "";
    width: 40px;
    height: 2px;
    background: #ec4176;
    position: relative;
    left: 3px;
    top: -4px;
    margin-right: 5px;
}

.input-group input[type="text"], .input-group input[type="text"]{
font-size:9pt;
letter-spacing:.9px;
font-weight:400;
}
.input-group input[type="text"]:focus, .input-group input[type="text"].focus{
color: #111;
}
.input-group span.icon{
display:inline-block;
padding:6px 12px;
margin-bottom:0; 
margin-left:0px; 
font-size:14px;
font-weight:400;
line-height:2;
text-align:center;
white-space:nowrap;
vertical-align:middle; 
color:#fff;
-webkit-transition: all 0.1s ease-in-out;
transition: all 0.1s ease-in-out;
}
.input-group-btn button span.icon{
background: #333;
}
.input-group-btn button:hover span.icon{
background: #ec4176;
-webkit-transition: all 0.2s ease-in-out;
transition: all 0.2s ease-in-out;
}

.catgorlist{
  padding: 0;
  margin: 0;
  list-style: none;
}
.catgorlist li{
  margin-bottom: 15px;
}
.catgorlist li a{
  position: relative;
  color: inherit;
  font-weight: 500;
  text-decoration: none;
  padding: 5px 0;
}
.catgorlist li a:after{
    content: "";
    width: 30px;
    height: 2px;
    background: rgba(255,255,255,.6);
    position: absolute;
    bottom: -2px;
    left: 0;
}

/* Blog end */

/* Blog comment */
#blog-comment {
margin-top: 60px;
padding: 0px;
}
#blog-comment h5 {
font-size: 14px;
font-weight: 600;
margin-bottom: 15px;
padding-bottom: 15px;
}
#blog-comment > ul, #blog-comment > li {
list-style: none;
padding-left: 0;
}
#blog-comment ul.children > ul {
list-style: none;
}
#blog-comment li .avatar {
position: absolute;
border-radius: 100%;
overflow: hidden;
}
#blog-comment li .avatar{
object-fit: cover;
width: 65px;
height: 65px;
}
#blog-comment li .avatar img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}
#blog-comment .comment {
font-size: 13px;
margin-left: 85px;
}
#blog-comment li {
font-size: 14px;
line-height: 1.6em;
padding-bottom: 0px;
}
#blog-comment li > li {
margin-left: 0px;
padding-bottom: 0px;
margin-top: 20px;
padding-top: 10px;
border-bottom: none;
}
#blog-comment li li .avatar {
position: absolute;
}
#blog-comment ul.children {
margin-left: 70px;
}
#blog-comment .pingback-entry {
margin-bottom: 20px;
}

#blog-comment p.no-comments {
color: #8a6d3b;
background-color: #43296f;
padding: 5px 15px;
margin-bottom: 20px;
border-radius: 4px;
font-style: italic;
border: 1px solid #faebcc;
}
#blog-comment > ul ul {
list-style: none;
margin-top: 10px;
}
.comment-info {
margin-left: 85px;
margin-bottom: 5px;
background: #43296f;
padding: 20px 30px;
}
.comment-info .c-name {
font-size: 1rem;
font-weight: 800;
}
.comment-info .c-reply {
display: block;
margin-top: 10px;
margin-bottom: 5px;
}
.comment-info .c-reply a {
color: #ddd;
font-size: .81rem;
text-transform: uppercase;
text-decoration: none;
font-weight: 700;
letter-spacing: .9px;
}
.comment-info .c-reply a:hover{
color: #ccc;
}
.comment-info .c-reply a::before {
content: "\f112";
margin-right: 5px;
font-family: fontawesome;
font-size: 9px;
color: #ddd;
}
.comment-info .c-date {
display: inline;
text-transform: uppercase;
font-size: 11px;
float: right;
font-weight: 400;
}
.comment-info .c-date i {
padding-right: 0px;
}
.comment-content.entry.clr p {
font-size: 14px;
margin: 15px 0;
}

/* comment form */
.comment-respond {
margin-top: 10px;
padding: 25px 30px;
background: #43296f;
}
.comment-form{
    margin: 15px 0;
}
#comment-form-wrapper h6 {
font-size: 11pt;
margin-bottom: 15px;
padding-bottom: 15px;
}
#comment-form-wrapper {
margin: 50px 0 50px 0;
padding: 0px;
}
#commentform input {
width: 100%;
font-size: 14px;
padding: 10px;
height: 40px;
letter-spacing: 0px;
color: inherit;
background: rgba(0, 0, 0, 0);
border: 1px solid rgba(255,255,255,.1);
-webkit-transition: all 0.3s ease;
-moz-transition: all 0.3s ease;
-o-transition: all 0.3s ease;
transition: all 0.2s ease;
}
#commentform input:focus, #commentform input:focus-visible {
background: rgba(0, 0, 0, 0);
border: 1px solid rgba(255,255,255,.1);
outline: none;
}
#commentform textarea {
width: 100%;
font-size: 14px;
padding: 10px;
height: 150px;
color: inherit;
background: rgba(0, 0, 0, 0);
border: 1px solid rgba(255,255,255,.1);
}
#commentform textarea:focus, #commentform textarea:focus-visible {
color: #f8f8f8;
border: 1px solid rgba(255,255,255,.1);
outline: none;
}
#commentform button.btn, #commentform input[type="submit"] {
width: 160px;
height: 46px;
font-size: .83rem;
font-weight: 700;
font-style: normal;
text-align: center;
text-transform: uppercase;
line-height: 38px;
color: #fff;
border: 0px solid rgba(0, 0, 0, 0);
background: #ec4176;
border-radius: 6px;
outline: none;
text-shadow: none;
margin: 15px auto 0;
margin-left: 0px;
padding: 0;
cursor: pointer;
-moz-transition: all 0.3s ease;
-ms-transition: all 0.3s ease;
-o-transition: all 0.3s ease;
transition: all 0.3s ease;
}
#commentform button.btn:hover, #commentform input[type="submit"]:hover {
color:#fff;
background:#262254;
}
#cancel-comment-reply-link {
color: red;
}
.comment-reply-title {
font-size: 1.3rem;
font-weight: 700;
line-height: 1.3333;
letter-spacing: 0;
}

/* search menu */
.search-container {
position: relative;
width: 100%;
padding: 5vh 15% 5vh;
}
#searchmenu{
position: relative;
}
.s-input-home {
color: #fff;
font-size: 1rem;
background: rgba(0,0,0,0);
width: 100%;
height: 46px;
padding: 0 0 0 15px;
border: 0px;
outline: none;
border: 1px solid rgba(255,255,255,.6);
border-top-right-radius: 4px;
border-bottom-right-radius: 4px;
}
.btn-s-input {
color: #fff;
position: absolute;
top: -1px;
right: 0px;
width: 56px;
height: 48px;
font-size: .9rem;
background: #ec4176;
border: 0px;
border-top-right-radius: 4px;
border-bottom-right-radius: 4px;
cursor: pointer;
-moz-transition: all 0.3s ease;
-ms-transition: all 0.3s ease;
-o-transition: all 0.3s ease;
transition: all 0.3s ease;
}
/* Blog comment end */

.ti-twitter::before {
  content:"𝕏";
  font-size:1.1em;
}

/* scroll onStep */
.onStep {
opacity: 0;
}
