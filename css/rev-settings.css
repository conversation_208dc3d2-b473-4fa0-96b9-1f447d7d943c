.fullwidthbanner-container{
	width: 100% !important;
	color: #fff;
	position:relative;
	padding: 0;
	overflow:hidden;
	background: #020b1c;
}
.tp-dottedoverlay{
	width: 0;
	height: 0;
}
.slide-big-heading{
	font-size: 44pt;
	font-weight: 700;
	text-align: center;
	text-shadow:0px 0px 7px rgba(0, 0, 0, 0.25);
}
.slide-sub-heading{
	font-size: 16pt;
	font-weight: 400;
	text-align: center;
	text-shadow:0px 0px 7px rgba(0, 0, 0, 0.25);
}
.btn-slider { 
	display: block;
	position:relative;
	float:left;
	width: auto;
	height: auto;
	font-size: 12pt !important;
	font-weight: 500;
	text-align: center;
	letter-spacing:1px;
	text-shadow: none;
	line-height: 15px;
	padding: 18px 40px !important;
    background: #f05500;
    border-radius: 30px;
	cursor: pointer;
	overflow: hidden;
	-ms-transform: scale(1, 1);
    transform: scale(1, 1);
	-moz-transition: all 0.6s ease;
	-ms-transition: all 0.6s ease;
	-o-transition: all 0.6s ease;
	transition: all 0.6s ease;
}
.btn-slider a{
	font-weight: 600;
	color: #fff;
	position: relative;
    top: -1px;
}
.btn-slider:hover { 
	background: #f07c05;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}
.btn-slider:hover .shine{
	-webkit-animation: sheen .9s alternate;
	animation: sheen .9s alternate;
} 
.btn-slider:hover a{ 
	color:#fff;
}
.tparrows.preview1{
	width: 70px;
	height: 70px;
}
.tparrows.preview1::after {
	font-size: 18px;
	width: 70px;
	height: 70px;
	line-height: 70px;
	background: rgba(0,0,0,.6);
}
.tparrows.preview1:hover::after {
	color: #fff;
	background: rgba(0,0,0,1);
}
.tparrows.preview1:hover .tp-arr-imgholder{
	display: none;
}
.tp-bannertimer{
	display: none !important;
}
@media only screen and (max-width: 500px) {
	.slide-big-heading{
	 font-size: 45pt;
	 margin-left: 30px;
	}
	.slide-sub-heading{
	 font-size: 24pt;
	 margin-left: 30px;
	 margin-top: 15px;
	}
	.btn-slider { 
	font-size: 13pt;
	margin-left: 30px;
	margin-top: 60px;
	padding: 10px 20px;
    }
}