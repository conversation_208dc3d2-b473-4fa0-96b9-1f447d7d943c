@media only screen and (max-width: 1199px) {
	#main-menu{
	display: none;
	}
	.menu-right {
		display: block;
	}
	#main-menu{
	position: relative;
	top: 20px;
	}
	.white nav ul li:hover>ul {
	display: unset;
	}
	header.init.sub{
		top: 0;
	}
	header.init{
	display: block;
	position: relative;
	padding: 40px 0;
	}
	.detailcontent .heading{
	margin: 5px 0 30px;
	}
	.detailcontent.text-right{
	text-align: center !important;
	}
	.d-none-mobile{
	display: none !important;
	}
	#showmobile{
	font-size: 1.1rem;
	display: block;
	color: #fff;
	cursor: pointer;
	}
	.menu-right.centered {
	position: absolute;
	top: 25px;
	right: 0px;
	}
	.blogcolumn, .servcices{
		margin-top: 15px;
	}
	.subfooter{
		display: block;
	}
	.subfooter .terms{
		display: block;
		margin-top: 10px;
	}
	/*main menu*/
	.menu-right .iconright li:last-child{
		padding-left: 10px;
		padding-right: 10px;
	}
	.navbar-default-white.navbar-fixed-top div .p-3-vh{
		display: block !important;
	}
	.logo.centered{
		display: unset;
		position: absolute;
		top: 25px;
		left: 15px;
	}
	.white nav{
		float: unset;
		top: 0;
		margin-top: 15px;
	}
	.white nav ul li{
		display: block;
		width: 100%;
		margin-right: 0;
	}
	.white nav ul li a{
		padding: 20px 10px 20px 10px;
		border-bottom: 1px solid rgba(255, 255, 255, .1);
	}
	.white nav ul li a.last{
		border-bottom: 0;
	}
	.white nav ul ul{
		position: relative;
		top: 0px;
	}
	.white nav ul ul li{
		width: 100%;
		box-shadow: none;
		border-bottom: 0;
	}
	.white nav ul ul li a{
		line-height: unset;
		padding: 20px 15px 0 10px;
		border-bottom: 0;
	}
	.bgimg{
		background-size: auto 100% !important;
	}
	.bgimg-section{
		background-size: auto !important;
	}
	.white nav ul li:hover>ul {
	display: none;
	}
	.white nav ul ul li{
		background: inherit;
	}
	#signin{
	width: max-content;
	padding: 6px 28px;
	margin-top: 20px;
	}
}
@media only screen and (max-width: 1024px) {
	.owl-nav{
		opacity: 1;
	}
	.imgbg-col{
		position: relative;
		max-height: 250px;
		overflow: hidden;
	}
	.slickproject .item .icon{
		opacity: 1;
		bottom: 30px;
	}
	.slickproject .item .desc{
		bottom: 30px;
	}
	.subfooter .subinside{
		display: block;
	}
	footer .s-social{
	 	margin-bottom: 30px;
	 }
	 .bgpurp .fly{
	  position: relative;
	  margin: 0 5% 0;
	  top: 0;
	}
}
@media only screen and (max-width: 768px) {
 #home .detailcontent{
 	padding: 0px;
 }
 .team{
 	display: block;
 }
 .team .img{
 	width: 60%;
 	margin: 0 auto;
 }
 .team .detail{
 	text-align: center;
 	width: auto;
 	padding-left: 0;
 	margin-top: 15px;
 }
 .timeline-container{
 	padding: 10px !important;
 }
}
@media only screen and (max-width: 660px) {
  
}
@media(max-width:414px) {

}
@media(max-width:360px) {

}
@media(max-width:320px) {
 
}